#!/usr/bin/env python3
"""
Example script demonstrating the new robust proxy rotation system
This script shows how to use the proxy system for data fetching with automatic failover
"""

import time
import logging
from datetime import datetime

# Import the new proxy management system
from proxy_config import ProxySystemConfig, DEFAULT_CONFIG
from proxy_manager import get_proxy_manager
from proxy_health import get_health_monitor
from proxy_blacklist import get_blacklist_detector
from proxy_retry import get_retry_handler, RetryStrategy, retry_on_failure

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def setup_proxy_system():
    """Initialize the proxy management system"""
    logger.info("Initializing robust proxy rotation system...")
    
    # Get system components
    proxy_manager = get_proxy_manager(DEFAULT_CONFIG)
    health_monitor = get_health_monitor(DEFAULT_CONFIG)
    blacklist_detector = get_blacklist_detector(DEFAULT_CONFIG)
    retry_handler = get_retry_handler(DEFAULT_CONFIG)
    
    # Log initial status
    proxy_stats = proxy_manager.get_pool_stats()
    logger.info(f"Proxy pool initialized: {proxy_stats['healthy_proxies']}/{proxy_stats['total_proxies']} healthy proxies")
    
    return proxy_manager, health_monitor, blacklist_detector, retry_handler


def simulate_api_call(stock_code: str, use_proxy: bool = True):
    """
    Simulate an API call that might fail
    This demonstrates how the proxy system handles failures
    """
    import random
    import requests
    
    # Simulate different types of failures
    failure_types = [
        None,  # Success
        requests.exceptions.ConnectionError("Connection failed"),
        requests.exceptions.Timeout("Request timeout"),
        requests.exceptions.HTTPError("403 Forbidden"),
        Exception("API rate limit exceeded"),
        Exception("返回空数据"),  # Chinese API limit message
    ]
    
    # 70% success rate for demonstration
    if random.random() < 0.7:
        # Simulate successful response
        time.sleep(random.uniform(0.1, 2.0))  # Simulate response time
        return {"stock_code": stock_code, "data": f"Mock data for {stock_code}"}
    else:
        # Simulate failure
        failure = random.choice(failure_types[1:])
        raise failure


@retry_on_failure(max_retries=3, strategy=RetryStrategy.EXPONENTIAL_BACKOFF, use_proxy=True)
def fetch_stock_data_with_decorator(stock_code: str):
    """Example using the retry decorator"""
    logger.info(f"Fetching data for {stock_code} using decorator")
    return simulate_api_call(stock_code)


def fetch_stock_data_manual(stock_code: str, retry_handler):
    """Example using manual retry handling"""
    logger.info(f"Fetching data for {stock_code} using manual retry")
    
    def _fetch():
        return simulate_api_call(stock_code)
    
    return retry_handler.execute_with_retry(
        _fetch,
        max_retries=3,
        strategy=RetryStrategy.EXPONENTIAL_BACKOFF,
        use_proxy=True,
        context_name=f"fetch_{stock_code}"
    )


def demonstrate_proxy_management():
    """Demonstrate proxy management features"""
    logger.info("=== Proxy Management Demonstration ===")
    
    # Initialize system
    proxy_manager, health_monitor, blacklist_detector, retry_handler = setup_proxy_system()
    
    # Test stock codes
    test_stocks = ["000001", "000002", "600000", "600036", "000858"]
    
    logger.info("Starting data fetching simulation...")
    
    for i, stock_code in enumerate(test_stocks):
        try:
            logger.info(f"\n--- Processing stock {i+1}/{len(test_stocks)}: {stock_code} ---")
            
            # Alternate between decorator and manual methods
            if i % 2 == 0:
                result = fetch_stock_data_with_decorator(stock_code)
            else:
                result = fetch_stock_data_manual(stock_code, retry_handler)
            
            logger.info(f"Successfully fetched data for {stock_code}: {result}")
            
        except Exception as e:
            logger.error(f"Failed to fetch data for {stock_code}: {e}")
        
        # Small delay between requests
        time.sleep(1)
    
    # Display final statistics
    display_statistics(proxy_manager, health_monitor, blacklist_detector, retry_handler)


def display_statistics(proxy_manager, health_monitor, blacklist_detector, retry_handler):
    """Display comprehensive system statistics"""
    logger.info("\n=== Final System Statistics ===")
    
    # Proxy pool statistics
    proxy_stats = proxy_manager.get_pool_stats()
    logger.info(f"Proxy Pool:")
    logger.info(f"  Total proxies: {proxy_stats['total_proxies']}")
    logger.info(f"  Healthy proxies: {proxy_stats['healthy_proxies']}")
    logger.info(f"  Near expiry proxies: {proxy_stats['near_expiry_proxies']} (剩余<30秒)")
    logger.info(f"  Expired proxies: {proxy_stats['expired_proxies']}")
    logger.info(f"  Blacklisted proxies: {proxy_stats['blacklisted_proxies']}")
    logger.info(f"  Average health score: {proxy_stats['average_health_score']:.2f}")
    logger.info(f"  Average remaining time: {proxy_stats['average_remaining_time']:.0f}秒")
    logger.info(f"  Min remaining time: {proxy_stats['min_remaining_time']:.0f}秒")
    logger.info(f"  Max remaining time: {proxy_stats['max_remaining_time']:.0f}秒")
    logger.info(f"  Pool utilization: {proxy_stats['pool_utilization']:.2%}")
    logger.info(f"  Expiry time: {proxy_stats['expiry_time_seconds']}秒 (3分钟)")
    
    # Health monitoring statistics
    health_stats = health_monitor.get_performance_summary()
    logger.info(f"\nHealth Monitoring:")
    logger.info(f"  Total requests: {health_stats['total_requests']}")
    logger.info(f"  Global success rate: {health_stats['global_success_rate']:.2%}")
    logger.info(f"  Healthy proxies: {health_stats['healthy_proxies']}")
    logger.info(f"  Uptime: {health_stats['uptime_hours']:.2f} hours")
    logger.info(f"  Requests per hour: {health_stats['requests_per_hour']:.1f}")
    
    # Blacklist statistics
    blacklist_stats = blacklist_detector.get_failure_stats()
    logger.info(f"\nBlacklist Detection:")
    logger.info(f"  Tracked proxies: {blacklist_stats['total_tracked_proxies']}")
    logger.info(f"  Blacklisted proxies: {blacklist_stats['blacklisted_proxies']}")
    logger.info(f"  Total failures: {blacklist_stats['total_failures']}")
    if blacklist_stats['failure_reasons']:
        logger.info(f"  Failure reasons: {blacklist_stats['failure_reasons']}")
    
    # Retry statistics
    retry_stats = retry_handler.get_retry_stats()
    logger.info(f"\nRetry Handler:")
    logger.info(f"  Total contexts: {retry_stats['total_contexts']}")
    logger.info(f"  Total attempts: {retry_stats['total_attempts']}")
    logger.info(f"  Overall success rate: {retry_stats['overall_success_rate']:.2%}")
    logger.info(f"  Circuit breaker states: {retry_stats['circuit_breaker_states']}")


def demonstrate_configuration():
    """Demonstrate configuration management"""
    logger.info("\n=== Configuration Demonstration ===")
    
    # Show default configuration
    config = DEFAULT_CONFIG
    logger.info("Default Configuration:")
    logger.info(f"  Max pool size: {config.pool.max_pool_size}")
    logger.info(f"  Min pool size: {config.pool.min_pool_size}")
    logger.info(f"  Health threshold: {config.health.success_rate_threshold}")
    logger.info(f"  Max retries: {config.retry.max_retries}")
    logger.info(f"  Base delay: {config.retry.base_delay}s")
    
    # Show configuration as dictionary
    config_dict = config.to_dict()
    logger.info(f"Configuration keys: {list(config_dict.keys())}")


def main():
    """Main demonstration function"""
    logger.info("Starting Robust Proxy Rotation System Demonstration")
    logger.info(f"Timestamp: {datetime.now()}")
    
    try:
        # Demonstrate configuration
        demonstrate_configuration()
        
        # Demonstrate proxy management
        demonstrate_proxy_management()
        
        logger.info("\n=== Demonstration Complete ===")
        logger.info("The proxy rotation system successfully handled all requests with automatic failover!")
        
    except Exception as e:
        logger.error(f"Demonstration failed: {e}")
        raise


if __name__ == "__main__":
    main()
