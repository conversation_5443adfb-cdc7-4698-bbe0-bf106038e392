#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
股票周线数据获取脚本 - 修复版本
解决卡住问题，添加超时和监控机制
"""

import os
import sys
import time
import threading
import signal
import queue
from concurrent.futures import ThreadPoolExecutor, as_completed
from contextlib import contextmanager
import pandas as pd
import requests
from sqlalchemy import create_engine, text
from sqlalchemy.pool import QueuePool
from sqlalchemy.exc import SQLAlchemyError

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from utils.logger import setup_logger

# 设置日志
logger = setup_logger("22_stock_week_adata_fixed")

# 尝试导入adata
try:
    import adata
    ADATA_AVAILABLE = True
    logger.info("adata库导入成功")
except ImportError as e:
    ADATA_AVAILABLE = False
    logger.error(f"adata库导入失败: {e}")
    sys.exit(1)

# 数据库配置
DB_CONFIG = {
    "host": "**********",
    "port": 3306,
    "user": "root",
    "password": "31490600",
    "database": "instockdb",
    "charset": "utf8mb4"
}

# 并发处理配置
CONCURRENT_WORKERS = 10  # 减少并发数
STOCKS_PER_WORKER = 200  # 每个工作线程处理的股票数量
WORKER_DELAY = 0.5  # 增加延迟

# 代理配置
USE_PROXY = True
PROXY_API_URL = "http://api2.xkdaili.com/tools/XApi.ashx?apikey=XKD70D31EF48B479CD67&qty=1&format=txt&split=0&sign=c43298623494e165a62d214593f07552&area=330100"
PROXY_POOL = []
PROXY_LOCK = threading.Lock()
PROXY_EXPIRY_TIME = 3 * 60  # 3分钟

# 超时和监控配置
API_TIMEOUT = 30  # API调用超时时间（秒）
MAX_RETRY_ATTEMPTS = 3  # 每个股票最大重试次数
RETRY_DELAY = 2.0  # 重试间隔（秒）
PROGRESS_REPORT_INTERVAL = 50  # 进度报告间隔

# 数据库连接池配置
DB_POOL_SIZE = 5
DB_MAX_OVERFLOW = 10
DB_POOL_TIMEOUT = 30

def log_progress(logger, message):
    """记录进度日志"""
    logger.info(f"[进度] {message}")

def log_error(logger, message):
    """记录错误日志"""
    logger.error(f"[错误] {message}")

def get_new_proxy():
    """获取新的代理服务器"""
    global PROXY_POOL
    
    try:
        response = requests.get(PROXY_API_URL, timeout=10)
        if response.status_code == 200:
            proxy = response.text.strip()
            if proxy and ":" in proxy:
                with PROXY_LOCK:
                    if proxy not in PROXY_POOL:
                        PROXY_POOL.append(proxy)
                        log_progress(logger, f"获取新代理: {proxy}")
                        return True
        return False
    except Exception as e:
        log_error(logger, f"获取代理失败: {str(e)}")
        return False

def get_proxy_from_pool():
    """从代理池获取一个可用的代理"""
    global PROXY_POOL
    
    if not USE_PROXY:
        return None
    
    with PROXY_LOCK:
        if PROXY_POOL:
            return PROXY_POOL[0]  # 使用第一个代理
    
    # 如果没有代理，尝试获取新的
    if get_new_proxy():
        with PROXY_LOCK:
            if PROXY_POOL:
                return PROXY_POOL[0]
    
    return None

def setup_proxy():
    """初始化代理池"""
    if not USE_PROXY:
        return True
    
    if not PROXY_POOL:
        return get_new_proxy()
    return True

@contextmanager
def db_connection():
    """数据库连接上下文管理"""
    engine = create_engine(
        f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@"
        f"{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}?charset={DB_CONFIG['charset']}",
        poolclass=QueuePool,
        pool_size=DB_POOL_SIZE,
        max_overflow=DB_MAX_OVERFLOW,
        pool_timeout=DB_POOL_TIMEOUT,
        pool_pre_ping=True,
    )
    conn = None
    try:
        conn = engine.connect()
        yield conn
    except SQLAlchemyError as e:
        log_error(logger, f"数据库连接失败: {str(e)}")
        raise
    finally:
        if conn:
            conn.close()
        engine.dispose()

class TimeoutError(Exception):
    """超时异常"""
    pass

def timeout_handler(signum, frame):
    """超时处理函数"""
    raise TimeoutError("API调用超时")

def get_stock_weekly_data_with_timeout(ts_code, start_date, end_date, proxy=None):
    """获取单只股票周线数据（带超时机制）"""
    try:
        # 设置超时信号
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(API_TIMEOUT)
        
        # 从ts_code中提取stock_code
        if "." in ts_code:
            stock_code, exchange = ts_code.split(".")
        else:
            stock_code = ts_code
            exchange = "SH"
        
        # 设置代理
        if USE_PROXY and proxy:
            adata.proxy(is_proxy=True, ip=proxy)
        
        # 转换日期格式
        start_date_formatted = f"{start_date[:4]}-{start_date[4:6]}-{start_date[6:8]}"
        end_date_formatted = f"{end_date[:4]}-{end_date[4:6]}-{end_date[6:8]}"
        
        # 调用API
        df = adata.stock.market.get_market(
            stock_code=stock_code,
            start_date=start_date_formatted,
            end_date=end_date_formatted,
            k_type=2,  # 周K线
            adjust_type=1,  # 前复权
        )
        
        # 取消超时信号
        signal.alarm(0)
        
        if df is None or df.empty:
            return pd.DataFrame()
        
        # 数据转换
        df_transformed = df.copy()
        df_transformed["ts_code"] = ts_code
        
        if "volume" in df_transformed.columns:
            df_transformed["vol"] = df_transformed["volume"]
        
        return df_transformed
        
    except TimeoutError:
        signal.alarm(0)
        log_error(logger, f"股票 {ts_code} API调用超时")
        return pd.DataFrame()
    except Exception as e:
        signal.alarm(0)
        log_error(logger, f"获取股票 {ts_code} 数据失败: {str(e)}")
        return pd.DataFrame()

def process_stock_with_retry(ts_code, start_date, end_date, proxy=None):
    """处理单只股票（带重试机制）"""
    for attempt in range(MAX_RETRY_ATTEMPTS):
        try:
            df = get_stock_weekly_data_with_timeout(ts_code, start_date, end_date, proxy)
            if not df.empty:
                return df
            
            if attempt < MAX_RETRY_ATTEMPTS - 1:
                log_progress(logger, f"股票 {ts_code} 第{attempt+1}次尝试返回空数据，{RETRY_DELAY}秒后重试")
                time.sleep(RETRY_DELAY)
            
        except Exception as e:
            if attempt < MAX_RETRY_ATTEMPTS - 1:
                log_error(logger, f"股票 {ts_code} 第{attempt+1}次尝试失败: {str(e)}，{RETRY_DELAY}秒后重试")
                time.sleep(RETRY_DELAY)
            else:
                log_error(logger, f"股票 {ts_code} 重试{MAX_RETRY_ATTEMPTS}次后仍失败")
    
    return pd.DataFrame()

def worker_process_stocks_fixed(stock_codes, start_date, end_date, worker_id):
    """工作线程处理股票数据（修复版本）"""
    try:
        log_progress(logger, f"工作线程 {worker_id} 开始处理 {len(stock_codes)} 只股票")
        
        # 确保代理已设置
        if USE_PROXY and not setup_proxy():
            log_error(logger, f"工作线程 {worker_id} 无法设置代理")
            return pd.DataFrame()
        
        all_data = []
        processed_count = 0
        success_count = 0
        
        for i, ts_code in enumerate(stock_codes):
            try:
                # 获取代理
                proxy = get_proxy_from_pool() if USE_PROXY else None
                
                # 处理股票
                df = process_stock_with_retry(ts_code, start_date, end_date, proxy)
                
                if not df.empty:
                    all_data.append(df)
                    success_count += 1
                
                processed_count += 1
                
                # 进度报告
                if processed_count % PROGRESS_REPORT_INTERVAL == 0 or processed_count == len(stock_codes):
                    success_rate = (success_count / processed_count * 100) if processed_count > 0 else 0
                    log_progress(logger, f"工作线程 {worker_id}: 已处理 {processed_count}/{len(stock_codes)} 只股票，成功 {success_count} 只 (成功率: {success_rate:.1f}%)")
                
                # 添加延迟
                time.sleep(WORKER_DELAY)
                
            except Exception as e:
                log_error(logger, f"工作线程 {worker_id} 处理股票 {ts_code} 异常: {str(e)}")
                processed_count += 1
                continue
        
        # 合并数据
        if all_data:
            result_df = pd.concat(all_data, ignore_index=True)
            log_progress(logger, f"工作线程 {worker_id} 完成，获取到 {len(result_df)} 条记录")
            return result_df
        else:
            log_progress(logger, f"工作线程 {worker_id} 完成，未获取到任何数据")
            return pd.DataFrame()
            
    except Exception as e:
        log_error(logger, f"工作线程 {worker_id} 发生错误: {str(e)}")
        return pd.DataFrame()

def get_stock_codes():
    """获取股票代码列表"""
    try:
        with db_connection() as conn:
            query = "SELECT ts_code FROM stock_basic WHERE ts_code IS NOT NULL ORDER BY ts_code"
            df = pd.read_sql(text(query), conn)
            stock_codes = df['ts_code'].tolist()
            log_progress(logger, f"从数据库获取到 {len(stock_codes)} 只股票代码")
            return stock_codes
    except Exception as e:
        log_error(logger, f"获取股票代码失败: {str(e)}")
        return []

def save_weekly_data(df):
    """保存数据到数据库"""
    if df.empty:
        log_progress(logger, "无数据需要保存")
        return

    try:
        with db_connection() as conn:
            # 先清空表
            conn.execute(text("TRUNCATE TABLE ts_stock_weekly"))
            log_progress(logger, "已清空 ts_stock_weekly 表")

            # 批量插入数据
            batch_size = 10000
            total_records = len(df)

            for i in range(0, total_records, batch_size):
                batch_df = df.iloc[i:i+batch_size]
                batch_df.to_sql(
                    "ts_stock_weekly",
                    conn,
                    if_exists="append",
                    index=False,
                    chunksize=1000
                )
                log_progress(logger, f"已保存 {min(i+batch_size, total_records)}/{total_records} 条记录")

            log_progress(logger, f"数据保存完成，共 {total_records} 条记录")

    except Exception as e:
        log_error(logger, f"保存数据失败: {str(e)}")
        raise

def main():
    """主函数"""
    try:
        log_progress(logger, "=== 股票周线数据获取开始 ===")
        start_time = time.time()

        # 获取股票代码
        stock_codes = get_stock_codes()
        if not stock_codes:
            log_error(logger, "未获取到股票代码，程序退出")
            return

        # 计算日期范围
        from datetime import datetime, timedelta
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=365*2)).strftime('%Y%m%d')  # 2年数据

        log_progress(logger, f"数据获取范围: {start_date} 至 {end_date}")
        log_progress(logger, f"股票数量: {len(stock_codes)}")
        log_progress(logger, f"并发配置: {CONCURRENT_WORKERS} 个工作线程，每线程处理 {STOCKS_PER_WORKER} 只股票")

        # 分批处理
        stock_batches = []
        for i in range(0, len(stock_codes), STOCKS_PER_WORKER):
            batch = stock_codes[i:i+STOCKS_PER_WORKER]
            stock_batches.append(batch)

        log_progress(logger, f"分成 {len(stock_batches)} 个批次进行处理")

        # 并发处理
        all_results = []
        with ThreadPoolExecutor(max_workers=CONCURRENT_WORKERS) as executor:
            # 提交任务
            future_to_batch = {
                executor.submit(worker_process_stocks_fixed, batch, start_date, end_date, i+1): i+1
                for i, batch in enumerate(stock_batches)
            }

            # 收集结果
            for future in as_completed(future_to_batch):
                worker_id = future_to_batch[future]
                try:
                    result_df = future.result(timeout=600)  # 10分钟超时
                    if not result_df.empty:
                        all_results.append(result_df)
                        log_progress(logger, f"工作线程 {worker_id} 完成，获取 {len(result_df)} 条记录")
                    else:
                        log_progress(logger, f"工作线程 {worker_id} 完成，无数据")
                except Exception as e:
                    log_error(logger, f"工作线程 {worker_id} 异常: {str(e)}")

        # 合并所有结果
        if all_results:
            final_df = pd.concat(all_results, ignore_index=True)
            log_progress(logger, f"数据合并完成，共 {len(final_df)} 条记录")

            # 保存到数据库
            save_weekly_data(final_df)

            # 统计信息
            total_time = time.time() - start_time
            success_rate = (len(final_df) / len(stock_codes) * 100) if stock_codes else 0

            log_progress(logger, f"=== 处理完成 ===")
            log_progress(logger, f"总耗时: {total_time:.2f} 秒")
            log_progress(logger, f"处理股票: {len(stock_codes)} 只")
            log_progress(logger, f"获取记录: {len(final_df)} 条")
            log_progress(logger, f"平均每只股票: {len(final_df)/len(stock_codes):.1f} 条记录" if stock_codes else "0 条记录")

        else:
            log_error(logger, "未获取到任何数据")

    except Exception as e:
        log_error(logger, f"主程序异常: {str(e)}")
        raise

if __name__ == "__main__":
    main()
