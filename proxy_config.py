"""
Proxy Configuration Module for Stock Analysis Engine
Centralized configuration management for proxy rotation system
"""

import os
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


@dataclass
class ProxyAPIConfig:
    """Configuration for proxy API endpoints"""
    primary_url: str = os.getenv(
        "PROXY_API_URL", 
        "http://api2.xkdaili.com/tools/XApi.ashx?apikey=XKD70D31EF48B479CD67&qty=1&format=txt&split=0&sign=c43298623494e165a62d214593f07552&area=330100"
    )
    backup_urls: List[str] = field(default_factory=lambda: [
        # Add backup proxy API URLs here if available
    ])
    timeout: int = 10
    max_retries: int = 3


@dataclass
class ProxyPoolConfig:
    """Configuration for proxy pool management"""
    max_pool_size: int = 50
    min_pool_size: int = 8  # 增加最小池大小以应对快速过期
    concurrent_proxy_count: int = 20
    expiry_time: int = 180  # 3分钟有效期 (API限制)
    refresh_threshold: float = 0.6  # 当池中60%代理过期时开始刷新
    health_check_interval: int = 30  # 每30秒检查一次健康状态
    preemptive_refresh_time: int = 150  # 提前30秒开始刷新代理 (2.5分钟)
    batch_acquisition_size: int = 5  # 每次批量获取的代理数量
    acquisition_interval: float = 0.5  # 获取代理之间的间隔时间


@dataclass
class HealthMonitorConfig:
    """Configuration for proxy health monitoring"""
    success_rate_threshold: float = 0.7  # 70% success rate minimum
    response_time_threshold: float = 10.0  # 10 seconds maximum response time
    consecutive_failure_threshold: int = 3  # Mark as unhealthy after 3 consecutive failures
    health_score_decay: float = 0.95  # Health score decay factor
    min_health_score: float = 0.1  # Minimum health score before removal
    max_health_score: float = 1.0  # Maximum health score
    health_history_size: int = 100  # Number of recent requests to track


@dataclass
class BlacklistDetectionConfig:
    """Configuration for blacklist detection"""
    # HTTP status codes that indicate blocking
    blocked_status_codes: List[int] = field(default_factory=lambda: [
        403,  # Forbidden
        429,  # Too Many Requests
        503,  # Service Unavailable
        502,  # Bad Gateway
        504,  # Gateway Timeout
        401,  # Unauthorized (sometimes used for IP blocking)
    ])
    
    # Response patterns that indicate blocking
    blocked_response_patterns: List[str] = field(default_factory=lambda: [
        "blocked",
        "rate limit",
        "access denied",
        "ip blocked",
        "too many requests",
        "service unavailable",
        "接口限制",
        "每分钟最多访问该接口",
        "返回空数据",
        "EOF",
        "connection reset",
        "connection refused",
    ])
    
    # Timeout thresholds
    connection_timeout: float = 30.0
    read_timeout: float = 60.0
    
    # Failure counting
    failure_window: int = 300  # 5 minutes window for failure counting
    max_failures_per_window: int = 5


@dataclass
class RetryConfig:
    """Configuration for retry logic"""
    max_retries: int = 5
    base_delay: float = 1.0  # Base delay in seconds
    max_delay: float = 300.0  # Maximum delay in seconds (5 minutes)
    exponential_base: float = 2.0  # Exponential backoff base
    jitter_factor: float = 0.1  # Random jitter factor (10%)
    
    # Different retry strategies for different error types
    network_error_max_retries: int = 3
    api_limit_max_retries: int = 10
    proxy_error_max_retries: int = 2
    
    # Cooldown periods for different error types
    api_limit_cooldown: int = 60  # 1 minute for API limits
    network_error_cooldown: int = 30  # 30 seconds for network errors
    proxy_error_cooldown: int = 10  # 10 seconds for proxy errors


@dataclass
class LoggingConfig:
    """Configuration for logging"""
    log_level: str = "INFO"
    log_proxy_performance: bool = True
    log_health_metrics: bool = True
    log_blacklist_events: bool = True
    performance_log_interval: int = 300  # Log performance every 5 minutes


@dataclass
class ProxySystemConfig:
    """Main configuration class that combines all proxy system settings"""
    api: ProxyAPIConfig = field(default_factory=ProxyAPIConfig)
    pool: ProxyPoolConfig = field(default_factory=ProxyPoolConfig)
    health: HealthMonitorConfig = field(default_factory=HealthMonitorConfig)
    blacklist: BlacklistDetectionConfig = field(default_factory=BlacklistDetectionConfig)
    retry: RetryConfig = field(default_factory=RetryConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    
    # Global settings
    enable_proxy: bool = True
    enable_health_monitoring: bool = True
    enable_blacklist_detection: bool = True
    enable_performance_logging: bool = True
    
    @classmethod
    def from_env(cls) -> 'ProxySystemConfig':
        """Create configuration from environment variables"""
        config = cls()
        
        # Override with environment variables if present
        if os.getenv("PROXY_POOL_MAX_SIZE"):
            config.pool.max_pool_size = int(os.getenv("PROXY_POOL_MAX_SIZE"))
        
        if os.getenv("PROXY_POOL_MIN_SIZE"):
            config.pool.min_pool_size = int(os.getenv("PROXY_POOL_MIN_SIZE"))
        
        if os.getenv("PROXY_EXPIRY_TIME"):
            config.pool.expiry_time = int(os.getenv("PROXY_EXPIRY_TIME"))
        
        if os.getenv("HEALTH_SUCCESS_THRESHOLD"):
            config.health.success_rate_threshold = float(os.getenv("HEALTH_SUCCESS_THRESHOLD"))
        
        if os.getenv("RETRY_MAX_RETRIES"):
            config.retry.max_retries = int(os.getenv("RETRY_MAX_RETRIES"))
        
        if os.getenv("RETRY_BASE_DELAY"):
            config.retry.base_delay = float(os.getenv("RETRY_BASE_DELAY"))
        
        if os.getenv("ENABLE_PROXY"):
            config.enable_proxy = os.getenv("ENABLE_PROXY").lower() == "true"
        
        return config
    
    def validate(self) -> bool:
        """Validate configuration parameters"""
        errors = []
        
        # Validate pool configuration
        if self.pool.min_pool_size >= self.pool.max_pool_size:
            errors.append("min_pool_size must be less than max_pool_size")
        
        if self.pool.refresh_threshold <= 0 or self.pool.refresh_threshold >= 1:
            errors.append("refresh_threshold must be between 0 and 1")
        
        # Validate health configuration
        if self.health.success_rate_threshold <= 0 or self.health.success_rate_threshold > 1:
            errors.append("success_rate_threshold must be between 0 and 1")
        
        if self.health.min_health_score >= self.health.max_health_score:
            errors.append("min_health_score must be less than max_health_score")
        
        # Validate retry configuration
        if self.retry.base_delay <= 0:
            errors.append("base_delay must be positive")
        
        if self.retry.max_delay <= self.retry.base_delay:
            errors.append("max_delay must be greater than base_delay")
        
        if errors:
            raise ValueError(f"Configuration validation failed: {'; '.join(errors)}")
        
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            "api": self.api.__dict__,
            "pool": self.pool.__dict__,
            "health": self.health.__dict__,
            "blacklist": self.blacklist.__dict__,
            "retry": self.retry.__dict__,
            "logging": self.logging.__dict__,
            "enable_proxy": self.enable_proxy,
            "enable_health_monitoring": self.enable_health_monitoring,
            "enable_blacklist_detection": self.enable_blacklist_detection,
            "enable_performance_logging": self.enable_performance_logging,
        }


# Global configuration instance
DEFAULT_CONFIG = ProxySystemConfig.from_env()

# Validate configuration on import
DEFAULT_CONFIG.validate()
