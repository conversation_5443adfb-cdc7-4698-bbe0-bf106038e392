"""
Core Proxy Manager for Stock Analysis Engine
Intelligent proxy pool management with health-based selection
"""

import time
import random
import threading
import requests
from typing import Optional, List, Dict, Any
from dataclasses import dataclass, field
from collections import defaultdict
import logging

from proxy_config import ProxySystemConfig, DEFAULT_CONFIG


@dataclass
class ProxyInfo:
    """Information about a proxy server"""
    address: str
    port: Optional[int] = None
    acquired_time: float = field(default_factory=time.time)
    health_score: float = 1.0
    success_count: int = 0
    failure_count: int = 0
    total_requests: int = 0
    last_used: float = field(default_factory=time.time)
    last_success: Optional[float] = None
    last_failure: Optional[float] = None
    consecutive_failures: int = 0
    average_response_time: float = 0.0
    is_blacklisted: bool = False
    blacklist_reason: Optional[str] = None
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate"""
        if self.total_requests == 0:
            return 1.0
        return self.success_count / self.total_requests
    
    @property
    def is_expired(self) -> bool:
        """Check if proxy is expired"""
        return time.time() - self.acquired_time > DEFAULT_CONFIG.pool.expiry_time

    @property
    def is_near_expiry(self) -> bool:
        """Check if proxy is near expiry (within 30 seconds)"""
        return time.time() - self.acquired_time > DEFAULT_CONFIG.pool.preemptive_refresh_time

    @property
    def remaining_lifetime(self) -> float:
        """Get remaining lifetime in seconds"""
        elapsed = time.time() - self.acquired_time
        return max(0, DEFAULT_CONFIG.pool.expiry_time - elapsed)
    
    @property
    def is_healthy(self) -> bool:
        """Check if proxy is healthy"""
        return (
            not self.is_blacklisted and
            not self.is_expired and
            self.health_score >= DEFAULT_CONFIG.health.min_health_score and
            self.consecutive_failures < DEFAULT_CONFIG.health.consecutive_failure_threshold
        )
    
    def update_success(self, response_time: float):
        """Update proxy stats after successful request"""
        self.success_count += 1
        self.total_requests += 1
        self.last_used = time.time()
        self.last_success = time.time()
        self.consecutive_failures = 0
        
        # Update average response time
        if self.average_response_time == 0:
            self.average_response_time = response_time
        else:
            self.average_response_time = (self.average_response_time * 0.8) + (response_time * 0.2)
        
        # Update health score
        self._update_health_score(True)
    
    def update_failure(self, reason: str = ""):
        """Update proxy stats after failed request"""
        self.failure_count += 1
        self.total_requests += 1
        self.last_used = time.time()
        self.last_failure = time.time()
        self.consecutive_failures += 1
        
        # Update health score
        self._update_health_score(False)
    
    def _update_health_score(self, success: bool):
        """Update health score based on request outcome"""
        if success:
            # Increase health score for success
            self.health_score = min(
                DEFAULT_CONFIG.health.max_health_score,
                self.health_score + 0.1
            )
        else:
            # Decrease health score for failure
            self.health_score = max(
                DEFAULT_CONFIG.health.min_health_score,
                self.health_score * DEFAULT_CONFIG.health.health_score_decay
            )
    
    def mark_blacklisted(self, reason: str):
        """Mark proxy as blacklisted"""
        self.is_blacklisted = True
        self.blacklist_reason = reason
        self.health_score = 0.0


class ProxyManager:
    """Centralized proxy pool manager with intelligent selection"""
    
    def __init__(self, config: ProxySystemConfig = DEFAULT_CONFIG):
        self.config = config
        self.proxy_pool: Dict[str, ProxyInfo] = {}
        self.lock = threading.RLock()
        self.logger = logging.getLogger(__name__)
        self.acquisition_stats = defaultdict(int)
        self.last_cleanup = time.time()
        self.last_batch_acquisition = 0

        # Initialize proxy pool
        if config.enable_proxy:
            self._initialize_pool()
            # 启动后台刷新线程来处理3分钟过期问题
            self._start_background_refresh()
    
    def _initialize_pool(self):
        """Initialize proxy pool with minimum number of proxies"""
        self.logger.info("初始化代理池...")

        # 批量获取初始代理
        self._batch_acquire_proxies(self.config.pool.min_pool_size)

        self.logger.info(f"代理池初始化完成，共获取 {len(self.proxy_pool)} 个代理")

    def _start_background_refresh(self):
        """启动后台代理刷新线程"""
        def refresh_worker():
            while True:
                try:
                    time.sleep(self.config.pool.health_check_interval)
                    self._proactive_refresh()
                except Exception as e:
                    self.logger.error(f"后台刷新线程错误: {e}")

        refresh_thread = threading.Thread(target=refresh_worker, daemon=True)
        refresh_thread.start()
        self.logger.info("后台代理刷新线程已启动")

    def _proactive_refresh(self):
        """主动刷新即将过期的代理"""
        with self.lock:
            near_expiry_proxies = [
                addr for addr, proxy in self.proxy_pool.items()
                if proxy.is_near_expiry and not proxy.is_expired
            ]

            if near_expiry_proxies:
                self.logger.info(f"发现 {len(near_expiry_proxies)} 个即将过期的代理，开始主动刷新")
                # 提前获取新代理
                self._batch_acquire_proxies(len(near_expiry_proxies))

    def _batch_acquire_proxies(self, count: int):
        """批量获取代理"""
        current_time = time.time()

        # 限制获取频率，避免过于频繁的API调用
        if current_time - self.last_batch_acquisition < self.config.pool.acquisition_interval:
            return

        self.last_batch_acquisition = current_time
        acquired_count = 0

        for i in range(count):
            try:
                if self._acquire_new_proxy():
                    acquired_count += 1

                # 在批量获取之间添加间隔
                if i < count - 1:
                    time.sleep(self.config.pool.acquisition_interval)

            except Exception as e:
                self.logger.warning(f"批量获取代理失败 ({i+1}/{count}): {e}")

        if acquired_count > 0:
            self.logger.info(f"批量获取完成，成功获取 {acquired_count}/{count} 个代理")
    
    def get_proxy(self) -> Optional[str]:
        """Get the best available proxy from the pool"""
        if not self.config.enable_proxy:
            return None
        
        with self.lock:
            # Clean up expired and unhealthy proxies
            self._cleanup_proxies()
            
            # Ensure minimum pool size
            self._ensure_minimum_pool_size()
            
            # Get healthy proxies sorted by health score
            healthy_proxies = [
                proxy for proxy in self.proxy_pool.values()
                if proxy.is_healthy
            ]
            
            if not healthy_proxies:
                self.logger.warning("No healthy proxies available")
                # Try to acquire new proxy immediately
                if self._acquire_new_proxy():
                    healthy_proxies = [
                        proxy for proxy in self.proxy_pool.values()
                        if proxy.is_healthy
                    ]
            
            if not healthy_proxies:
                return None
            
            # Weighted selection based on health score
            proxy = self._select_proxy_weighted(healthy_proxies)
            
            if proxy:
                self.logger.debug(f"Selected proxy {proxy.address} (health: {proxy.health_score:.2f})")
                return proxy.address
            
            return None
    
    def _select_proxy_weighted(self, proxies: List[ProxyInfo]) -> Optional[ProxyInfo]:
        """Select proxy using weighted selection based on health scores and remaining lifetime"""
        if not proxies:
            return None

        # 计算综合权重：健康分数 + 剩余生命周期权重
        weighted_proxies = []
        for proxy in proxies:
            # 健康分数权重 (0-1)
            health_weight = proxy.health_score

            # 剩余生命周期权重 (0-1)，剩余时间越长权重越高
            lifetime_weight = proxy.remaining_lifetime / self.config.pool.expiry_time

            # 综合权重：70%健康分数 + 30%剩余时间
            combined_weight = health_weight * 0.7 + lifetime_weight * 0.3

            weighted_proxies.append((proxy, combined_weight))

        # 按权重排序，选择权重最高的代理
        weighted_proxies.sort(key=lambda x: x[1], reverse=True)

        # 从前3个最佳代理中随机选择，增加一些随机性
        top_proxies = weighted_proxies[:min(3, len(weighted_proxies))]
        selected_proxy, weight = random.choice(top_proxies)

        self.logger.debug(f"选择代理 {selected_proxy.address}，"
                         f"健康分数: {selected_proxy.health_score:.2f}，"
                         f"剩余时间: {selected_proxy.remaining_lifetime:.0f}秒，"
                         f"综合权重: {weight:.2f}")

        return selected_proxy
    
    def report_success(self, proxy_address: str, response_time: float):
        """Report successful request for a proxy"""
        with self.lock:
            if proxy_address in self.proxy_pool:
                self.proxy_pool[proxy_address].update_success(response_time)
                self.logger.debug(f"Proxy {proxy_address} success reported (response_time: {response_time:.2f}s)")
    
    def report_failure(self, proxy_address: str, reason: str = ""):
        """Report failed request for a proxy"""
        with self.lock:
            if proxy_address in self.proxy_pool:
                self.proxy_pool[proxy_address].update_failure(reason)
                self.logger.debug(f"Proxy {proxy_address} failure reported: {reason}")
    
    def blacklist_proxy(self, proxy_address: str, reason: str):
        """Mark a proxy as blacklisted"""
        with self.lock:
            if proxy_address in self.proxy_pool:
                self.proxy_pool[proxy_address].mark_blacklisted(reason)
                self.logger.warning(f"Proxy {proxy_address} blacklisted: {reason}")
    
    def _acquire_new_proxy(self) -> bool:
        """Acquire a new proxy from the API"""
        try:
            response = requests.get(
                self.config.api.primary_url,
                timeout=self.config.api.timeout
            )
            
            if response.status_code == 200:
                proxy_address = response.text.strip()
                if proxy_address and proxy_address not in self.proxy_pool:
                    proxy_info = ProxyInfo(address=proxy_address)
                    self.proxy_pool[proxy_address] = proxy_info
                    self.acquisition_stats['success'] += 1
                    self.logger.info(f"Acquired new proxy: {proxy_address}")
                    return True
            
            self.acquisition_stats['failed'] += 1
            return False
            
        except Exception as e:
            self.acquisition_stats['error'] += 1
            self.logger.error(f"Failed to acquire new proxy: {e}")
            return False
    
    def _cleanup_proxies(self):
        """Remove expired and unhealthy proxies"""
        current_time = time.time()

        # 由于3分钟过期时间很短，更频繁地清理（每30秒）
        if current_time - self.last_cleanup < 30:
            return

        to_remove = []
        expired_count = 0
        unhealthy_count = 0

        for address, proxy in self.proxy_pool.items():
            should_remove = False
            reason = []

            if proxy.is_expired:
                should_remove = True
                reason.append("已过期")
                expired_count += 1
            elif proxy.health_score < self.config.health.min_health_score:
                should_remove = True
                reason.append(f"健康分数过低({proxy.health_score:.2f})")
                unhealthy_count += 1
            elif proxy.consecutive_failures >= self.config.health.consecutive_failure_threshold:
                should_remove = True
                reason.append(f"连续失败({proxy.consecutive_failures}次)")
                unhealthy_count += 1

            if should_remove:
                to_remove.append((address, ", ".join(reason)))

        # 移除不健康的代理
        for address, reason in to_remove:
            removed_proxy = self.proxy_pool.pop(address, None)
            if removed_proxy:
                self.logger.info(f"移除代理 {address}: {reason}，"
                               f"剩余时间: {removed_proxy.remaining_lifetime:.0f}秒")

        if to_remove:
            self.logger.info(f"代理清理完成: 移除 {len(to_remove)} 个代理 "
                           f"(过期: {expired_count}, 不健康: {unhealthy_count})")

            # 清理后立即补充代理
            self._ensure_minimum_pool_size()

        self.last_cleanup = current_time
    
    def _ensure_minimum_pool_size(self):
        """Ensure pool has minimum number of healthy proxies"""
        healthy_count = sum(1 for proxy in self.proxy_pool.values() if proxy.is_healthy)
        total_count = len(self.proxy_pool)

        if healthy_count < self.config.pool.min_pool_size:
            needed = self.config.pool.min_pool_size - healthy_count
            self.logger.info(f"代理池低于最小大小 ({healthy_count}/{self.config.pool.min_pool_size})，"
                           f"需要获取 {needed} 个新代理")

            # 批量获取代理，但限制单次获取数量
            batch_size = min(needed, self.config.pool.batch_acquisition_size)
            self._batch_acquire_proxies(batch_size)

        # 如果总代理数量过多，清理一些即将过期的代理
        elif total_count > self.config.pool.max_pool_size:
            near_expiry_proxies = [
                addr for addr, proxy in self.proxy_pool.items()
                if proxy.is_near_expiry
            ]

            if near_expiry_proxies:
                # 移除一些即将过期的代理为新代理腾出空间
                to_remove = near_expiry_proxies[:total_count - self.config.pool.max_pool_size]
                for addr in to_remove:
                    removed_proxy = self.proxy_pool.pop(addr, None)
                    if removed_proxy:
                        self.logger.info(f"移除即将过期的代理 {addr}，"
                                       f"剩余时间: {removed_proxy.remaining_lifetime:.0f}秒")
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """Get current pool statistics"""
        with self.lock:
            healthy_proxies = [p for p in self.proxy_pool.values() if p.is_healthy]
            near_expiry_proxies = [p for p in self.proxy_pool.values() if p.is_near_expiry and not p.is_expired]
            expired_proxies = [p for p in self.proxy_pool.values() if p.is_expired]

            # 计算平均剩余时间
            if self.proxy_pool:
                avg_remaining_time = sum(p.remaining_lifetime for p in self.proxy_pool.values()) / len(self.proxy_pool)
                min_remaining_time = min(p.remaining_lifetime for p in self.proxy_pool.values())
                max_remaining_time = max(p.remaining_lifetime for p in self.proxy_pool.values())
            else:
                avg_remaining_time = min_remaining_time = max_remaining_time = 0

            return {
                'total_proxies': len(self.proxy_pool),
                'healthy_proxies': len(healthy_proxies),
                'near_expiry_proxies': len(near_expiry_proxies),
                'expired_proxies': len(expired_proxies),
                'blacklisted_proxies': sum(1 for p in self.proxy_pool.values() if p.is_blacklisted),
                'average_health_score': sum(p.health_score for p in healthy_proxies) / len(healthy_proxies) if healthy_proxies else 0,
                'average_remaining_time': avg_remaining_time,
                'min_remaining_time': min_remaining_time,
                'max_remaining_time': max_remaining_time,
                'acquisition_stats': dict(self.acquisition_stats),
                'pool_utilization': len(self.proxy_pool) / self.config.pool.max_pool_size,
                'expiry_time_seconds': self.config.pool.expiry_time
            }


# Global proxy manager instance
_proxy_manager = None
_manager_lock = threading.Lock()


def get_proxy_manager(config: ProxySystemConfig = DEFAULT_CONFIG) -> ProxyManager:
    """Get global proxy manager instance (singleton)"""
    global _proxy_manager
    
    with _manager_lock:
        if _proxy_manager is None:
            _proxy_manager = ProxyManager(config)
        return _proxy_manager
